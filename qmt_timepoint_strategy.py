# coding:gbk
"""
QMT策略脚本：获取000001股票在9:35、9:40、9:45三个时间点的数据并存储到CSV文件
使用方法：在QMT中创建新策略，将此代码复制到策略中运行
注意：由于QMT环境限制，数据将保存到CSV文件而不是数据库
"""

import pandas as pd
from datetime import datetime, timedelta
import time
import os

# 数据保存配置
DATA_SAVE_PATH = "C:/QMT_Data/"  # 修改为您希望保存数据的路径
CSV_FILENAME = "stock_timepoint_data.csv"

# 目标股票和时间点
TARGET_STOCK = "000001.SZ"
TARGET_TIMES = ["093500", "094000", "094500"]  # 9:35, 9:40, 9:45
TIME_LABELS = ["9:35", "9:40", "9:45"]

def ensure_data_directory():
    """确保数据目录存在"""
    try:
        if not os.path.exists(DATA_SAVE_PATH):
            os.makedirs(DATA_SAVE_PATH)
            print(f"创建数据目录: {DATA_SAVE_PATH}")
        return True
    except Exception as e:
        print(f"创建数据目录失败: {e}")
        return False

def load_existing_data():
    """加载现有的CSV数据"""
    csv_path = os.path.join(DATA_SAVE_PATH, CSV_FILENAME)
    try:
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path)
            print(f"加载现有数据: {len(df)} 条记录")
            return df
        else:
            # 创建空的DataFrame with proper columns
            columns = [
                'stock_code', 'trade_date',
                'time_935_price', 'time_935_volume', 'time_935_amount',
                'time_940_price', 'time_940_volume', 'time_940_amount',
                'time_945_price', 'time_945_volume', 'time_945_amount',
                'created_at'
            ]
            df = pd.DataFrame(columns=columns)
            print("创建新的数据文件")
            return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return pd.DataFrame()

def save_data_to_csv(df):
    """保存数据到CSV文件"""
    csv_path = os.path.join(DATA_SAVE_PATH, CSV_FILENAME)
    try:
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {csv_path}")
        return True
    except Exception as e:
        print(f"保存数据失败: {e}")
        return False

def fetch_5min_data_qmt(ContextInfo, stock_code, trade_date):
    """使用QMT API获取5分钟数据"""
    try:
        # 获取5分钟数据，扩大时间范围以确保包含目标时间点
        start_time = trade_date + "093000"
        end_time = trade_date + "095000"

        # 使用QMT API获取5分钟数据
        data = ContextInfo.get_market_data_ex(
            fields=['time', 'close', 'volume', 'amount', 'open', 'high', 'low'],
            stock_code=[stock_code],
            period='5m',
            start_time=start_time,
            end_time=end_time,
            fill_data=True,
            subscribe=False
        )

        if stock_code in data and not data[stock_code].empty:
            df = data[stock_code].copy()
            print(f"获取到 {len(df)} 条5分钟数据")
            return df
        else:
            print(f"未获取到 {stock_code} 在 {trade_date} 的5分钟数据")
            return None

    except Exception as e:
        print(f"获取5分钟数据失败: {e}")
        return None

def extract_timepoint_data_qmt(df, stock_code, trade_date):
    """从5分钟数据中提取特定时间点的数据"""
    timepoint_data = {
        'stock_code': stock_code,
        'trade_date': trade_date,
        'time_935_price': None, 'time_935_volume': None, 'time_935_amount': None,
        'time_940_price': None, 'time_940_volume': None, 'time_940_amount': None,
        'time_945_price': None, 'time_945_volume': None, 'time_945_amount': None
    }

    if df is None or df.empty:
        return timepoint_data

    try:
        # 将索引转换为字符串格式以便匹配
        df_copy = df.copy()
        df_copy['time_str'] = df_copy.index.astype(str)

        # 对于5分钟K线，需要找到包含目标时间点的K线
        # 9:35 -> 应该在 9:35-9:40 的K线中
        # 9:40 -> 应该在 9:40-9:45 的K线中
        # 9:45 -> 应该在 9:45-9:50 的K线中

        target_5min_times = ["093500", "094000", "094500"]  # 对应5分钟K线的开始时间

        # 提取各时间点数据
        for i, target_time in enumerate(target_5min_times):
            # 查找匹配的5分钟K线
            matching_rows = df_copy[df_copy['time_str'].str.contains(target_time)]

            if not matching_rows.empty:
                row = matching_rows.iloc[0]
                time_key = f"time_{target_time[:3]}{target_time[3:]}"  # time_935, time_940, time_945

                timepoint_data[f'{time_key}_price'] = float(row['close']) if pd.notna(row['close']) else None
                timepoint_data[f'{time_key}_volume'] = int(row['volume']) if pd.notna(row['volume']) else None
                timepoint_data[f'{time_key}_amount'] = float(row['amount']) if pd.notna(row['amount']) else None

                print(f"找到 {TIME_LABELS[i]} 对应的5分钟K线数据: 价格={row['close']}, 成交量={row['volume']}")
            else:
                print(f"未找到 {TIME_LABELS[i]} 对应的5分钟K线数据")

        return timepoint_data

    except Exception as e:
        print(f"提取时间点数据失败: {e}")
        return timepoint_data

def store_timepoint_data_csv(timepoint_data):
    """将时间点数据存储到CSV文件"""
    try:
        # 加载现有数据
        df = load_existing_data()

        # 添加创建时间
        timepoint_data['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 检查是否已存在该日期的数据
        existing_mask = (df['stock_code'] == timepoint_data['stock_code']) & \
                       (df['trade_date'] == timepoint_data['trade_date'])

        if existing_mask.any():
            # 更新现有记录
            for key, value in timepoint_data.items():
                df.loc[existing_mask, key] = value
            print(f"更新 {timepoint_data['stock_code']} 在 {timepoint_data['trade_date']} 的时间点数据")
        else:
            # 添加新记录
            new_row = pd.DataFrame([timepoint_data])
            df = pd.concat([df, new_row], ignore_index=True)
            print(f"添加 {timepoint_data['stock_code']} 在 {timepoint_data['trade_date']} 的时间点数据")

        # 保存到CSV
        success = save_data_to_csv(df)
        return success

    except Exception as e:
        print(f"存储数据失败: {e}")
        return False

def process_single_date(ContextInfo, trade_date):
    """处理单个交易日的数据"""
    print(f"开始处理 {trade_date} 的数据...")

    # 获取5分钟数据
    data_5min = fetch_5min_data_qmt(ContextInfo, TARGET_STOCK, trade_date)

    # 提取时间点数据
    timepoint_data = extract_timepoint_data_qmt(data_5min, TARGET_STOCK, trade_date)

    # 存储到CSV文件
    success = store_timepoint_data_csv(timepoint_data)

    if success:
        print(f"✓ {trade_date} 处理完成")
    else:
        print(f"✗ {trade_date} 处理失败")

    return success

# QMT策略函数
def init(ContextInfo):
    """初始化函数"""
    print("=== QMT时间点数据获取策略启动 ===")
    print(f"目标股票: {TARGET_STOCK}")
    print(f"目标时间点: {TIME_LABELS}")
    print(f"数据保存路径: {DATA_SAVE_PATH}")

    # 确保数据目录存在
    ensure_data_directory()

    # 设置要处理的日期范围（最近30天）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)

    ContextInfo.start_date = start_date.strftime('%Y%m%d')
    ContextInfo.end_date = end_date.strftime('%Y%m%d')
    ContextInfo.current_date = start_date
    ContextInfo.processed_count = 0

    print(f"处理日期范围: {ContextInfo.start_date} 到 {ContextInfo.end_date}")

def handlebar(ContextInfo):
    """主处理函数"""
    try:
        # 检查是否还有日期需要处理
        if ContextInfo.current_date <= datetime.strptime(ContextInfo.end_date, '%Y%m%d'):
            trade_date_str = ContextInfo.current_date.strftime('%Y%m%d')
            
            # 只处理工作日（简单检查，不包含节假日）
            if ContextInfo.current_date.weekday() < 5:  # 周一到周五
                success = process_single_date(ContextInfo, trade_date_str)
                if success:
                    ContextInfo.processed_count += 1
                
                # 添加延迟避免请求过快
                time.sleep(1)
            
            # 移动到下一天
            ContextInfo.current_date += timedelta(days=1)
        else:
            print(f"\n=== 处理完成 ===")
            print(f"总共处理了 {ContextInfo.processed_count} 个交易日")
            
    except Exception as e:
        print(f"处理过程中出错: {e}")

def after_init(ContextInfo):
    """初始化后函数"""
    print("策略初始化完成，开始数据处理...")
    # ContextInfo 参数在此函数中暂未使用，但保留以符合QMT接口要求

# 如果需要单独测试某个日期，可以使用以下函数
def test_single_date(ContextInfo, test_date="20241201"):
    """测试单个日期的数据获取"""
    print(f"测试日期: {test_date}")
    ensure_data_directory()
    success = process_single_date(ContextInfo, test_date)
    return success
