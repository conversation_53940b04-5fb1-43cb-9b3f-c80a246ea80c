# coding:gbk
"""
QMT策略脚本：获取000001股票在9:35、9:40、9:45三个时间点的数据并存储到数据库
使用方法：在QMT中创建新策略，将此代码复制到策略中运行
"""

import pandas as pd
import pymysql
from datetime import datetime, timedelta
import time

# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '12345678',
    'database': 'Daily Line',
    'charset': 'utf8mb4'
}

# 目标股票和时间点
TARGET_STOCK = "000001.SZ"
TARGET_TIMES = ["093500", "094000", "094500"]  # 9:35, 9:40, 9:45
TIME_LABELS = ["9:35", "9:40", "9:45"]

def create_database_connection():
    """创建数据库连接"""
    try:
        connection = pymysql.connect(**DATABASE_CONFIG)
        return connection
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def create_timepoint_table():
    """创建时间点数据表"""
    connection = create_database_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS stock_timepoint_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            stock_code VARCHAR(20) NOT NULL,
            trade_date DATE NOT NULL,
            time_935_price DECIMAL(10,3),
            time_935_volume BIGINT,
            time_935_amount DECIMAL(15,2),
            time_940_price DECIMAL(10,3),
            time_940_volume BIGINT,
            time_940_amount DECIMAL(15,2),
            time_945_price DECIMAL(10,3),
            time_945_volume BIGINT,
            time_945_amount DECIMAL(15,2),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_stock_date (stock_code, trade_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        cursor.execute(create_table_sql)
        connection.commit()
        print("时间点数据表创建/验证成功")
        return True
        
    except Exception as e:
        print(f"创建表失败: {e}")
        return False
    finally:
        connection.close()

def fetch_minute_data_qmt(ContextInfo, stock_code, trade_date):
    """使用QMT API获取分钟数据"""
    try:
        # 下载历史数据（如果需要）
        download_history_data(stock_code, "1m", trade_date, trade_date)
        
        # 获取1分钟数据
        start_time = trade_date + "093000"
        end_time = trade_date + "095000"
        
        data = ContextInfo.get_market_data_ex(
            fields=['time', 'close', 'volume', 'amount', 'open', 'high', 'low'],
            stock_code=[stock_code],
            period='1m',
            start_time=start_time,
            end_time=end_time,
            fill_data=True,
            subscribe=False
        )
        
        if stock_code in data and not data[stock_code].empty:
            df = data[stock_code].copy()
            print(f"获取到 {len(df)} 条分钟数据")
            return df
        else:
            print(f"未获取到 {stock_code} 在 {trade_date} 的数据")
            return None
            
    except Exception as e:
        print(f"获取分钟数据失败: {e}")
        return None

def extract_timepoint_data_qmt(df, stock_code, trade_date):
    """从分钟数据中提取特定时间点的数据"""
    timepoint_data = {
        'stock_code': stock_code,
        'trade_date': trade_date,
        'time_935_price': None, 'time_935_volume': None, 'time_935_amount': None,
        'time_940_price': None, 'time_940_volume': None, 'time_940_amount': None,
        'time_945_price': None, 'time_945_volume': None, 'time_945_amount': None
    }
    
    if df is None or df.empty:
        return timepoint_data
    
    try:
        # 将索引转换为字符串格式以便匹配
        df_copy = df.copy()
        df_copy['time_str'] = df_copy.index.astype(str)
        
        # 提取各时间点数据
        for i, target_time in enumerate(TARGET_TIMES):
            target_time_str = trade_date + target_time
            
            # 查找匹配的时间点
            matching_rows = df_copy[df_copy['time_str'].str.contains(target_time)]
            
            if not matching_rows.empty:
                row = matching_rows.iloc[0]
                time_key = f"time_{target_time[:3]}{target_time[3:]}"  # time_935, time_940, time_945
                
                timepoint_data[f'{time_key}_price'] = float(row['close']) if pd.notna(row['close']) else None
                timepoint_data[f'{time_key}_volume'] = int(row['volume']) if pd.notna(row['volume']) else None
                timepoint_data[f'{time_key}_amount'] = float(row['amount']) if pd.notna(row['amount']) else None
                
                print(f"找到 {TIME_LABELS[i]} 数据: 价格={row['close']}, 成交量={row['volume']}")
            else:
                print(f"未找到 {TIME_LABELS[i]} 的数据")
        
        return timepoint_data
        
    except Exception as e:
        print(f"提取时间点数据失败: {e}")
        return timepoint_data

def store_timepoint_data_db(timepoint_data):
    """将时间点数据存储到数据库"""
    connection = create_database_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        insert_sql = """
        INSERT INTO stock_timepoint_data (
            stock_code, trade_date,
            time_935_price, time_935_volume, time_935_amount,
            time_940_price, time_940_volume, time_940_amount,
            time_945_price, time_945_volume, time_945_amount
        ) VALUES (
            %(stock_code)s, %(trade_date)s,
            %(time_935_price)s, %(time_935_volume)s, %(time_935_amount)s,
            %(time_940_price)s, %(time_940_volume)s, %(time_940_amount)s,
            %(time_945_price)s, %(time_945_volume)s, %(time_945_amount)s
        ) ON DUPLICATE KEY UPDATE
            time_935_price = VALUES(time_935_price),
            time_935_volume = VALUES(time_935_volume),
            time_935_amount = VALUES(time_935_amount),
            time_940_price = VALUES(time_940_price),
            time_940_volume = VALUES(time_940_volume),
            time_940_amount = VALUES(time_940_amount),
            time_945_price = VALUES(time_945_price),
            time_945_volume = VALUES(time_945_volume),
            time_945_amount = VALUES(time_945_amount),
            updated_at = CURRENT_TIMESTAMP
        """
        
        cursor.execute(insert_sql, timepoint_data)
        connection.commit()
        print(f"成功存储 {timepoint_data['stock_code']} 在 {timepoint_data['trade_date']} 的时间点数据")
        return True
        
    except Exception as e:
        print(f"存储数据失败: {e}")
        return False
    finally:
        connection.close()

def process_single_date(ContextInfo, trade_date):
    """处理单个交易日的数据"""
    print(f"开始处理 {trade_date} 的数据...")
    
    # 获取分钟数据
    minute_data = fetch_minute_data_qmt(ContextInfo, TARGET_STOCK, trade_date)
    
    # 提取时间点数据
    timepoint_data = extract_timepoint_data_qmt(minute_data, TARGET_STOCK, trade_date)
    
    # 存储到数据库
    success = store_timepoint_data_db(timepoint_data)
    
    if success:
        print(f"✓ {trade_date} 处理完成")
    else:
        print(f"✗ {trade_date} 处理失败")
    
    return success

# QMT策略函数
def init(ContextInfo):
    """初始化函数"""
    print("=== QMT时间点数据获取策略启动 ===")
    print(f"目标股票: {TARGET_STOCK}")
    print(f"目标时间点: {TIME_LABELS}")
    
    # 创建数据库表
    create_timepoint_table()
    
    # 设置要处理的日期范围（最近30天）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    ContextInfo.start_date = start_date.strftime('%Y%m%d')
    ContextInfo.end_date = end_date.strftime('%Y%m%d')
    ContextInfo.current_date = start_date
    ContextInfo.processed_count = 0
    
    print(f"处理日期范围: {ContextInfo.start_date} 到 {ContextInfo.end_date}")

def handlebar(ContextInfo):
    """主处理函数"""
    try:
        # 检查是否还有日期需要处理
        if ContextInfo.current_date <= datetime.strptime(ContextInfo.end_date, '%Y%m%d'):
            trade_date_str = ContextInfo.current_date.strftime('%Y%m%d')
            
            # 只处理工作日（简单检查，不包含节假日）
            if ContextInfo.current_date.weekday() < 5:  # 周一到周五
                success = process_single_date(ContextInfo, trade_date_str)
                if success:
                    ContextInfo.processed_count += 1
                
                # 添加延迟避免请求过快
                time.sleep(1)
            
            # 移动到下一天
            ContextInfo.current_date += timedelta(days=1)
        else:
            print(f"\n=== 处理完成 ===")
            print(f"总共处理了 {ContextInfo.processed_count} 个交易日")
            
    except Exception as e:
        print(f"处理过程中出错: {e}")

def after_init(ContextInfo):
    """初始化后函数"""
    print("策略初始化完成，开始数据处理...")

# 如果需要单独测试某个日期，可以使用以下函数
def test_single_date(ContextInfo, test_date="20241201"):
    """测试单个日期的数据获取"""
    print(f"测试日期: {test_date}")
    create_timepoint_table()
    success = process_single_date(ContextInfo, test_date)
    return success
