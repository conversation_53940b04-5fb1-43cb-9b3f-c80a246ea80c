# coding:gbk
"""
QMT简化版时间点数据获取策略
获取000001股票在9:35、9:40、9:45三个时间点的5分钟K线数据
数据保存到本地CSV文件
"""

from datetime import datetime, timedelta
import pandas as pd

# 配置参数
TARGET_STOCK = "000001.SZ"
TARGET_TIMES = ["093500", "094000", "094500"]  # 9:35, 9:40, 9:45对应的5分钟K线开始时间
TIME_LABELS = ["9:35", "9:40", "9:45"]

def get_timepoint_data_for_date(ContextInfo, trade_date):
    """获取指定日期的时间点数据"""
    print(f"正在获取 {trade_date} 的5分钟K线数据...")

    try:
        # 获取当天的5分钟数据
        start_time = trade_date + "093000"
        end_time = trade_date + "095000"

        data = ContextInfo.get_market_data_ex(
            fields=['time', 'close', 'volume', 'amount', 'open', 'high', 'low'],
            stock_code=[TARGET_STOCK],
            period='5m',
            start_time=start_time,
            end_time=end_time,
            fill_data=True,
            subscribe=False
        )

        if TARGET_STOCK not in data or data[TARGET_STOCK].empty:
            print(f"  未获取到 {trade_date} 的5分钟数据")
            return None

        df = data[TARGET_STOCK]
        print(f"  获取到 {len(df)} 条5分钟K线数据")
        
        # 提取时间点数据
        result = {
            'stock_code': TARGET_STOCK,
            'trade_date': trade_date,
            'time_935_price': None, 'time_935_volume': None, 'time_935_amount': None,
            'time_940_price': None, 'time_940_volume': None, 'time_940_amount': None,
            'time_945_price': None, 'time_945_volume': None, 'time_945_amount': None
        }
        
        # 将索引转换为字符串以便匹配
        df_copy = df.copy()
        df_copy['time_str'] = df_copy.index.astype(str)
        
        # 查找各时间点对应的5分钟K线数据
        # 对于5分钟K线：
        # 9:35 -> 查找 9:35-9:40 的K线（开始时间 093500）
        # 9:40 -> 查找 9:40-9:45 的K线（开始时间 094000）
        # 9:45 -> 查找 9:45-9:50 的K线（开始时间 094500）

        for i, target_time in enumerate(TARGET_TIMES):
            matching_rows = df_copy[df_copy['time_str'].str.contains(target_time)]

            if not matching_rows.empty:
                row = matching_rows.iloc[0]
                time_key = f"time_{target_time[:3]}{target_time[3:]}"

                result[f'{time_key}_price'] = float(row['close']) if pd.notna(row['close']) else None
                result[f'{time_key}_volume'] = int(row['volume']) if pd.notna(row['volume']) else None
                result[f'{time_key}_amount'] = float(row['amount']) if pd.notna(row['amount']) else None

                print(f"  找到 {TIME_LABELS[i]} 对应的5分钟K线: 价格={row['close']:.3f}, 成交量={row['volume']}, 开盘={row['open']:.3f}, 最高={row['high']:.3f}, 最低={row['low']:.3f}")
            else:
                print(f"  未找到 {TIME_LABELS[i]} 对应的5分钟K线数据")
        
        return result
        
    except Exception as e:
        print(f"获取 {trade_date} 数据失败: {e}")
        return None

def save_results_to_file(results, filename="timepoint_data.csv"):
    """保存结果到CSV文件"""
    try:
        if not results:
            print("没有数据需要保存")
            return
        
        df = pd.DataFrame(results)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filename}")
        print(f"共保存 {len(df)} 条记录")
        
        # 显示统计信息
        print("\n数据统计:")
        for time_point in ['935', '940', '945']:
            price_col = f'time_{time_point}_price'
            if price_col in df.columns:
                valid_count = df[price_col].notna().sum()
                print(f"  {time_point[:1]}:{time_point[1:]} 有效数据: {valid_count}/{len(df)}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

def init(ContextInfo):
    """初始化函数"""
    print("=== QMT简化版时间点数据获取策略 ===")
    print(f"目标股票: {TARGET_STOCK}")
    print(f"目标时间点: {TIME_LABELS}")
    
    # 设置处理参数
    ContextInfo.results = []
    ContextInfo.current_index = 0
    
    # 生成要处理的日期列表（最近10个交易日）
    end_date = datetime.now()
    date_list = []
    
    for i in range(20):  # 检查最近20天，筛选出工作日
        check_date = end_date - timedelta(days=i)
        if check_date.weekday() < 5:  # 周一到周五
            date_list.append(check_date.strftime('%Y%m%d'))
        if len(date_list) >= 10:  # 只要10个交易日
            break
    
    ContextInfo.date_list = sorted(date_list)
    print(f"将处理以下日期: {ContextInfo.date_list}")

def handlebar(ContextInfo):
    """主处理函数"""
    try:
        # 检查是否还有日期需要处理
        if ContextInfo.current_index < len(ContextInfo.date_list):
            trade_date = ContextInfo.date_list[ContextInfo.current_index]
            
            # 获取该日期的时间点数据
            result = get_timepoint_data_for_date(ContextInfo, trade_date)
            
            if result:
                ContextInfo.results.append(result)
                print(f"✓ 完成 {trade_date} ({ContextInfo.current_index + 1}/{len(ContextInfo.date_list)})")
            else:
                print(f"✗ 失败 {trade_date} ({ContextInfo.current_index + 1}/{len(ContextInfo.date_list)})")
            
            ContextInfo.current_index += 1
            
        else:
            # 所有日期处理完成，保存结果
            if ContextInfo.results:
                filename = f"timepoint_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                save_results_to_file(ContextInfo.results, filename)
                print(f"\n=== 处理完成 ===")
                print(f"成功处理: {len(ContextInfo.results)} 个交易日")
            else:
                print("没有获取到任何数据")
            
            # 重置以避免重复处理
            ContextInfo.results = []
            ContextInfo.current_index = len(ContextInfo.date_list)
            
    except Exception as e:
        print(f"处理过程中出错: {e}")

def after_init(ContextInfo):
    """初始化后函数"""
    print("策略初始化完成，开始数据获取...")

# 测试单个日期的函数
def test_today(ContextInfo):
    """测试获取今天的数据"""
    today = datetime.now().strftime('%Y%m%d')
    print(f"测试获取今天 ({today}) 的数据...")
    
    result = get_timepoint_data_for_date(ContextInfo, today)
    if result:
        save_results_to_file([result], f"test_{today}.csv")
        print("测试完成")
    else:
        print("测试失败")

# 使用说明
"""
使用方法：
1. 在QMT中创建新策略
2. 将此代码复制到策略编辑器
3. 运行策略
4. 查看控制台输出和生成的CSV文件

注意事项：
- 确保QMT已登录且有数据权限
- 策略会自动获取最近10个交易日的5分钟K线数据
- 数据保存在QMT运行目录下的CSV文件中
- 获取的是5分钟K线数据，不是精确的时间点数据
- 如果某个时间点没有对应的5分钟K线，对应字段将为空

5分钟K线时间对应关系：
- 9:35数据 -> 9:35-9:40的5分钟K线
- 9:40数据 -> 9:40-9:45的5分钟K线
- 9:45数据 -> 9:45-9:50的5分钟K线

CSV文件包含以下列：
- stock_code: 股票代码
- trade_date: 交易日期
- time_935_price/volume/amount: 9:35-9:40 K线的收盘价/成交量/成交额
- time_940_price/volume/amount: 9:40-9:45 K线的收盘价/成交量/成交额
- time_945_price/volume/amount: 9:45-9:50 K线的收盘价/成交量/成交额
"""
