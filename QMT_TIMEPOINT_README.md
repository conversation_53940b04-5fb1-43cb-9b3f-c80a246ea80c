# QMT时间点数据获取系统

本系统用于获取股票000001在9:35、9:40、9:45三个特定时间点的行情数据，并存储到"Daily Line"数据库中。

## 文件说明

### 1. `qmt_timepoint_strategy.py` - QMT策略脚本
**用途**: 在QMT平台内运行的策略脚本
**功能**: 
- 使用QMT API获取分钟级行情数据
- 提取特定时间点的价格、成交量、成交额
- 自动存储到MySQL数据库

### 2. `fetch_qmt_timepoint_data.py` - 独立数据获取脚本
**用途**: 在QMT外部环境运行的数据获取脚本
**功能**:
- 模拟QMT API调用（需要替换为实际API）
- 批量处理历史数据
- 数据验证和错误处理

### 3. `timepoint_data_manager.py` - 数据管理工具
**用途**: 数据分析和管理工具
**功能**:
- 查看和分析存储的时间点数据
- 生成趋势图表和统计报告
- 导出数据到Excel

## 数据库结构

### 表名: `stock_timepoint_data`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INT | 主键，自增 |
| stock_code | VARCHAR(20) | 股票代码 (000001.SZ) |
| trade_date | DATE | 交易日期 |
| time_935_price | DECIMAL(10,3) | 9:35时点价格 |
| time_935_volume | BIGINT | 9:35时点成交量 |
| time_935_amount | DECIMAL(15,2) | 9:35时点成交额 |
| time_940_price | DECIMAL(10,3) | 9:40时点价格 |
| time_940_volume | BIGINT | 9:40时点成交量 |
| time_940_amount | DECIMAL(15,2) | 9:40时点成交额 |
| time_945_price | DECIMAL(10,3) | 9:45时点价格 |
| time_945_volume | BIGINT | 9:45时点成交量 |
| time_945_amount | DECIMAL(15,2) | 9:45时点成交额 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 使用方法

### 方法一：在QMT中使用（推荐）

1. **打开QMT平台**
2. **创建新策略**:
   - 点击"策略" -> "新建策略"
   - 选择"Python策略"
3. **复制代码**:
   - 将 `qmt_timepoint_strategy.py` 的内容复制到策略编辑器
4. **配置数据库**:
   - 确认数据库连接参数正确
   - 确保MySQL服务正在运行
5. **运行策略**:
   - 点击"运行"开始数据获取
   - 查看控制台输出了解进度

### 方法二：使用独立脚本

1. **安装依赖**:
```bash
pip install pandas sqlalchemy pymysql
```

2. **运行数据获取**:
```bash
python fetch_qmt_timepoint_data.py
```

3. **数据管理**:
```bash
python timepoint_data_manager.py
```

## QMT API使用说明

### 关键API函数

1. **下载历史数据**:
```python
download_history_data(stock_code, "1m", start_date, end_date)
```

2. **获取行情数据**:
```python
data = ContextInfo.get_market_data_ex(
    fields=['time', 'close', 'volume', 'amount'],
    stock_code=[stock_code],
    period='1m',
    start_time=start_time,
    end_time=end_time
)
```

### 数据获取流程

1. **下载基础数据**: 使用 `download_history_data` 确保本地有足够的历史数据
2. **获取分钟数据**: 使用 `get_market_data_ex` 获取指定时间范围的1分钟数据
3. **提取时间点**: 从分钟数据中筛选出9:35、9:40、9:45的数据
4. **存储数据**: 将提取的数据存储到MySQL数据库

## 配置说明

### 数据库配置
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '12345678',
    'database': 'Daily Line',
    'charset': 'utf8mb4'
}
```

### 目标设置
```python
TARGET_STOCK = "000001.SZ"  # 平安银行
TARGET_TIMES = ["093500", "094000", "094500"]  # 9:35, 9:40, 9:45
```

## 数据分析功能

### 1. 基本统计
- 数据完整性分析
- 价格统计（均值、最值、标准差）
- 成交量统计

### 2. 趋势分析
- 价格趋势图
- 成交量趋势图
- 数据分布箱线图

### 3. 数据导出
- Excel格式导出
- 包含原始数据和统计摘要

## 注意事项

### 1. QMT环境要求
- 确保QMT客户端已登录
- 需要有相应的数据权限
- 建议在交易时间外运行以获取完整数据

### 2. 数据库要求
- MySQL 5.7+ 或 MariaDB 10.2+
- 确保数据库服务正在运行
- 用户需要有创建表和插入数据的权限

### 3. 网络和性能
- 获取大量历史数据时注意API调用频率限制
- 建议分批处理，避免一次性获取过多数据
- 监控数据库存储空间

### 4. 数据质量
- 某些时间点可能没有交易数据（停牌、节假日等）
- 系统会自动处理缺失数据，在数据库中存储为NULL
- 建议定期检查数据完整性

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证连接参数（主机、端口、用户名、密码）
   - 确认数据库"Daily Line"是否存在

2. **QMT API调用失败**
   - 确认QMT客户端已登录
   - 检查股票代码格式是否正确
   - 验证是否有相应的数据权限

3. **数据缺失**
   - 检查目标时间是否为交易时间
   - 确认股票在指定日期是否正常交易
   - 验证QMT本地是否有相应的历史数据

### 日志查看
- QMT策略: 查看QMT控制台输出
- 独立脚本: 查看 `qmt_timepoint_fetch.log` 文件

## 扩展功能

### 1. 多股票支持
修改 `TARGET_STOCK` 为股票列表，批量处理多只股票

### 2. 更多时间点
在 `TARGET_TIMES` 中添加更多时间点，如开盘、收盘等

### 3. 实时监控
结合QMT的实时数据推送功能，实现实时数据采集

### 4. 数据分析
基于采集的数据进行技术分析、模式识别等

## 联系支持

如有问题，请检查：
1. QMT官方文档: https://dict.thinktrader.net/
2. 数据库连接和权限设置
3. Python环境和依赖包安装
