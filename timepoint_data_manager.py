#!/usr/bin/env python3
"""
时间点数据管理工具
用于查看、分析和管理存储在数据库中的时间点数据
"""

import pandas as pd
import pymysql
from sqlalchemy import create_engine, text
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging

# 数据库配置
DATABASE_NAME = "Daily Line"
DATABASE_URL = f"mysql+pymysql://root:12345678@localhost:3306/{DATABASE_NAME}?charset=utf8mb4"

# 设置中文字体（如果需要显示中文图表）
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(DATABASE_URL)

def get_timepoint_data(stock_code="000001.SZ", start_date=None, end_date=None):
    """获取时间点数据"""
    try:
        base_sql = """
        SELECT 
            stock_code,
            trade_date,
            time_935_price, time_935_volume, time_935_amount,
            time_940_price, time_940_volume, time_940_amount,
            time_945_price, time_945_volume, time_945_amount,
            created_at, updated_at
        FROM stock_timepoint_data 
        WHERE stock_code = :stock_code
        """
        
        params = {'stock_code': stock_code}
        
        if start_date:
            base_sql += " AND trade_date >= :start_date"
            params['start_date'] = start_date
            
        if end_date:
            base_sql += " AND trade_date <= :end_date"
            params['end_date'] = end_date
            
        base_sql += " ORDER BY trade_date DESC"
        
        with engine.connect() as connection:
            df = pd.read_sql(text(base_sql), connection, params=params)
            
        logger.info(f"获取到 {len(df)} 条时间点数据")
        return df
        
    except Exception as e:
        logger.error(f"获取时间点数据失败: {e}")
        return pd.DataFrame()

def analyze_timepoint_patterns(df):
    """分析时间点数据的模式"""
    if df.empty:
        logger.warning("没有数据可供分析")
        return
    
    print("\n" + "="*60)
    print("时间点数据分析报告")
    print("="*60)
    
    # 基本统计
    total_records = len(df)
    records_with_935 = df['time_935_price'].notna().sum()
    records_with_940 = df['time_940_price'].notna().sum()
    records_with_945 = df['time_945_price'].notna().sum()
    
    print(f"总记录数: {total_records}")
    print(f"包含9:35数据的记录: {records_with_935} ({records_with_935/total_records*100:.1f}%)")
    print(f"包含9:40数据的记录: {records_with_940} ({records_with_940/total_records*100:.1f}%)")
    print(f"包含9:45数据的记录: {records_with_945} ({records_with_945/total_records*100:.1f}%)")
    
    # 价格统计
    print(f"\n价格统计:")
    for time_point in ['935', '940', '945']:
        price_col = f'time_{time_point}_price'
        if price_col in df.columns and df[price_col].notna().any():
            prices = df[price_col].dropna()
            print(f"  {time_point[:1]}:{time_point[1:]} - 均价: {prices.mean():.3f}, "
                  f"最高: {prices.max():.3f}, 最低: {prices.min():.3f}, "
                  f"标准差: {prices.std():.3f}")
    
    # 成交量统计
    print(f"\n成交量统计:")
    for time_point in ['935', '940', '945']:
        volume_col = f'time_{time_point}_volume'
        if volume_col in df.columns and df[volume_col].notna().any():
            volumes = df[volume_col].dropna()
            print(f"  {time_point[:1]}:{time_point[1:]} - 平均: {volumes.mean():.0f}, "
                  f"最大: {volumes.max():.0f}, 最小: {volumes.min():.0f}")

def plot_timepoint_trends(df, save_path=None):
    """绘制时间点数据趋势图"""
    if df.empty:
        logger.warning("没有数据可供绘图")
        return
    
    # 准备数据
    df_plot = df.copy()
    df_plot['trade_date'] = pd.to_datetime(df_plot['trade_date'])
    df_plot = df_plot.sort_values('trade_date')
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('000001股票时间点数据趋势分析', fontsize=16)
    
    # 价格趋势
    ax1 = axes[0, 0]
    for time_point, label in [('935', '9:35'), ('940', '9:40'), ('945', '9:45')]:
        price_col = f'time_{time_point}_price'
        if price_col in df_plot.columns:
            valid_data = df_plot[df_plot[price_col].notna()]
            if not valid_data.empty:
                ax1.plot(valid_data['trade_date'], valid_data[price_col], 
                        marker='o', markersize=3, label=label, alpha=0.7)
    
    ax1.set_title('价格趋势')
    ax1.set_xlabel('交易日期')
    ax1.set_ylabel('价格 (元)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 成交量趋势
    ax2 = axes[0, 1]
    for time_point, label in [('935', '9:35'), ('940', '9:40'), ('945', '9:45')]:
        volume_col = f'time_{time_point}_volume'
        if volume_col in df_plot.columns:
            valid_data = df_plot[df_plot[volume_col].notna()]
            if not valid_data.empty:
                ax2.plot(valid_data['trade_date'], valid_data[volume_col]/10000, 
                        marker='o', markersize=3, label=label, alpha=0.7)
    
    ax2.set_title('成交量趋势')
    ax2.set_xlabel('交易日期')
    ax2.set_ylabel('成交量 (万股)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 价格分布
    ax3 = axes[1, 0]
    price_data = []
    labels = []
    for time_point, label in [('935', '9:35'), ('940', '9:40'), ('945', '9:45')]:
        price_col = f'time_{time_point}_price'
        if price_col in df_plot.columns:
            prices = df_plot[price_col].dropna()
            if not prices.empty:
                price_data.append(prices)
                labels.append(label)
    
    if price_data:
        ax3.boxplot(price_data, labels=labels)
        ax3.set_title('价格分布')
        ax3.set_ylabel('价格 (元)')
        ax3.grid(True, alpha=0.3)
    
    # 成交量分布
    ax4 = axes[1, 1]
    volume_data = []
    labels = []
    for time_point, label in [('935', '9:35'), ('940', '9:40'), ('945', '9:45')]:
        volume_col = f'time_{time_point}_volume'
        if volume_col in df_plot.columns:
            volumes = df_plot[volume_col].dropna()
            if not volumes.empty:
                volume_data.append(volumes/10000)
                labels.append(label)
    
    if volume_data:
        ax4.boxplot(volume_data, labels=labels)
        ax4.set_title('成交量分布')
        ax4.set_ylabel('成交量 (万股)')
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"图表已保存到: {save_path}")
    
    plt.show()

def export_to_excel(df, filename=None):
    """导出数据到Excel"""
    if df.empty:
        logger.warning("没有数据可供导出")
        return
    
    if filename is None:
        filename = f"timepoint_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    try:
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 原始数据
            df.to_excel(writer, sheet_name='原始数据', index=False)
            
            # 统计摘要
            summary_data = []
            for time_point in ['935', '940', '945']:
                price_col = f'time_{time_point}_price'
                volume_col = f'time_{time_point}_volume'
                amount_col = f'time_{time_point}_amount'
                
                if price_col in df.columns:
                    prices = df[price_col].dropna()
                    volumes = df[volume_col].dropna()
                    amounts = df[amount_col].dropna()
                    
                    summary_data.append({
                        '时间点': f"{time_point[:1]}:{time_point[1:]}",
                        '记录数': len(prices),
                        '平均价格': prices.mean() if not prices.empty else None,
                        '最高价格': prices.max() if not prices.empty else None,
                        '最低价格': prices.min() if not prices.empty else None,
                        '平均成交量': volumes.mean() if not volumes.empty else None,
                        '平均成交额': amounts.mean() if not amounts.empty else None
                    })
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='统计摘要', index=False)
        
        logger.info(f"数据已导出到: {filename}")
        
    except Exception as e:
        logger.error(f"导出Excel失败: {e}")

def main():
    """主函数"""
    print("时间点数据管理工具")
    print("1. 查看最近30天数据")
    print("2. 查看指定日期范围数据")
    print("3. 生成分析报告和图表")
    print("4. 导出数据到Excel")
    
    choice = input("\n请选择操作 (1-4): ").strip()
    
    if choice == "1":
        # 最近30天
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)
        df = get_timepoint_data(start_date=start_date, end_date=end_date)
        
    elif choice == "2":
        # 指定日期范围
        start_date = input("请输入开始日期 (YYYY-MM-DD): ").strip()
        end_date = input("请输入结束日期 (YYYY-MM-DD): ").strip()
        df = get_timepoint_data(start_date=start_date, end_date=end_date)
        
    elif choice == "3":
        # 分析报告
        df = get_timepoint_data()
        if not df.empty:
            analyze_timepoint_patterns(df)
            plot_timepoint_trends(df)
        
    elif choice == "4":
        # 导出Excel
        df = get_timepoint_data()
        if not df.empty:
            filename = input("请输入文件名 (留空使用默认名称): ").strip()
            export_to_excel(df, filename if filename else None)
    
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
