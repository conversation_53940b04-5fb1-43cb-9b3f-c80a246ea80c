#!/usr/bin/env python3
"""
QMT API script to fetch specific time point data (9:35, 9:40, 9:45) for stock 000001
and store it in the "Daily Line" database
"""

import pandas as pd
from sqlalchemy import create_engine, text
import logging
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional

# Database Configuration
DATABASE_NAME = "Daily Line"
DATABASE_URL = f"mysql+pymysql://root:12345678@localhost:3306/{DATABASE_NAME}?charset=utf8mb4"

# Target stock and time points
TARGET_STOCK = "000001.SZ"
TARGET_TIMES = ["093500", "094000", "094500"]  # 9:35, 9:40, 9:45
TIME_LABELS = ["9:35", "9:40", "9:45"]

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qmt_timepoint_fetch.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Create database engine
engine = create_engine(DATABASE_URL)

def create_timepoint_table():
    """Create table for storing time point data"""
    try:
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS stock_timepoint_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            stock_code VARCHAR(20) NOT NULL,
            trade_date DATE NOT NULL,
            time_935_price DECIMAL(10,3),
            time_935_volume BIGINT,
            time_935_amount DECIMAL(15,2),
            time_940_price DECIMAL(10,3),
            time_940_volume BIGINT,
            time_940_amount DECIMAL(15,2),
            time_945_price DECIMAL(10,3),
            time_945_volume BIGINT,
            time_945_amount DECIMAL(15,2),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_stock_date (stock_code, trade_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        with engine.connect() as connection:
            connection.execute(text(create_table_sql))
            connection.commit()
            logger.info("Time point data table created/verified")
            
    except Exception as e:
        logger.error(f"Error creating time point table: {e}")
        raise

def fetch_minute_data_for_date(stock_code: str, trade_date: str) -> Optional[pd.DataFrame]:
    """
    Fetch 1-minute data for a specific date using QMT API
    This is a placeholder function - you need to implement the actual QMT API calls
    """
    try:
        # NOTE: This is a placeholder implementation
        # You need to replace this with actual QMT API calls
        # The QMT API would be something like:
        
        # In QMT environment, you would use:
        # data = ContextInfo.get_market_data_ex(
        #     fields=['time', 'close', 'volume', 'amount'],
        #     stock_code=[stock_code],
        #     period='1m',
        #     start_time=trade_date + '093000',
        #     end_time=trade_date + '095000'
        # )
        
        # For now, we'll create sample data structure
        logger.warning("Using placeholder data - replace with actual QMT API calls")
        
        # Sample data structure that QMT would return
        sample_data = {
            'time': [
                f"{trade_date}093500",
                f"{trade_date}094000", 
                f"{trade_date}094500"
            ],
            'close': [10.50, 10.52, 10.48],
            'volume': [1000000, 1200000, 950000],
            'amount': [10500000.0, 12624000.0, 9956000.0]
        }
        
        df = pd.DataFrame(sample_data)
        df['time'] = pd.to_datetime(df['time'], format='%Y%m%d%H%M%S')
        
        return df
        
    except Exception as e:
        logger.error(f"Error fetching minute data for {stock_code} on {trade_date}: {e}")
        return None

def extract_timepoint_data(df: pd.DataFrame, trade_date: str) -> Dict:
    """Extract data for specific time points from minute data"""
    try:
        timepoint_data = {
            'stock_code': TARGET_STOCK,
            'trade_date': trade_date,
            'time_935_price': None,
            'time_935_volume': None,
            'time_935_amount': None,
            'time_940_price': None,
            'time_940_volume': None,
            'time_940_amount': None,
            'time_945_price': None,
            'time_945_volume': None,
            'time_945_amount': None
        }
        
        if df is None or df.empty:
            return timepoint_data
        
        # Extract data for each target time
        for i, target_time in enumerate(TARGET_TIMES):
            target_datetime = pd.to_datetime(f"{trade_date}{target_time}", format='%Y%m%d%H%M%S')
            
            # Find the closest time point
            time_diff = abs(df['time'] - target_datetime)
            closest_idx = time_diff.idxmin()
            
            if time_diff.iloc[closest_idx] <= pd.Timedelta(minutes=1):  # Within 1 minute tolerance
                row = df.iloc[closest_idx]
                time_prefix = f"time_{TARGET_TIMES[i][:3]}{TARGET_TIMES[i][3:]}"  # time_935, time_940, time_945
                
                timepoint_data[f'{time_prefix}_price'] = float(row['close'])
                timepoint_data[f'{time_prefix}_volume'] = int(row['volume'])
                timepoint_data[f'{time_prefix}_amount'] = float(row['amount'])
                
                logger.info(f"Found data for {TIME_LABELS[i]}: Price={row['close']}, Volume={row['volume']}")
            else:
                logger.warning(f"No data found for {TIME_LABELS[i]} on {trade_date}")
        
        return timepoint_data
        
    except Exception as e:
        logger.error(f"Error extracting timepoint data: {e}")
        return timepoint_data

def store_timepoint_data(timepoint_data: Dict):
    """Store time point data to database"""
    try:
        insert_sql = """
        INSERT INTO stock_timepoint_data (
            stock_code, trade_date,
            time_935_price, time_935_volume, time_935_amount,
            time_940_price, time_940_volume, time_940_amount,
            time_945_price, time_945_volume, time_945_amount
        ) VALUES (
            :stock_code, :trade_date,
            :time_935_price, :time_935_volume, :time_935_amount,
            :time_940_price, :time_940_volume, :time_940_amount,
            :time_945_price, :time_945_volume, :time_945_amount
        ) ON DUPLICATE KEY UPDATE
            time_935_price = VALUES(time_935_price),
            time_935_volume = VALUES(time_935_volume),
            time_935_amount = VALUES(time_935_amount),
            time_940_price = VALUES(time_940_price),
            time_940_volume = VALUES(time_940_volume),
            time_940_amount = VALUES(time_940_amount),
            time_945_price = VALUES(time_945_price),
            time_945_volume = VALUES(time_945_volume),
            time_945_amount = VALUES(time_945_amount),
            updated_at = CURRENT_TIMESTAMP
        """
        
        with engine.connect() as connection:
            connection.execute(text(insert_sql), timepoint_data)
            connection.commit()
            logger.info(f"Stored timepoint data for {timepoint_data['stock_code']} on {timepoint_data['trade_date']}")
            
    except Exception as e:
        logger.error(f"Error storing timepoint data: {e}")

def fetch_timepoint_data_for_date_range(start_date: str, end_date: str):
    """Fetch time point data for a date range"""
    try:
        start_dt = datetime.strptime(start_date, '%Y%m%d')
        end_dt = datetime.strptime(end_date, '%Y%m%d')
        
        current_date = start_dt
        processed_count = 0
        
        while current_date <= end_dt:
            trade_date_str = current_date.strftime('%Y%m%d')
            
            # Skip weekends (basic check - you might want to add holiday checking)
            if current_date.weekday() < 5:  # Monday = 0, Friday = 4
                logger.info(f"Processing {trade_date_str}...")
                
                # Fetch minute data for the date
                minute_data = fetch_minute_data_for_date(TARGET_STOCK, trade_date_str)
                
                # Extract timepoint data
                timepoint_data = extract_timepoint_data(minute_data, trade_date_str)
                
                # Store to database
                store_timepoint_data(timepoint_data)
                
                processed_count += 1
                
                # Add small delay to avoid overwhelming the API
                time.sleep(0.1)
            
            current_date += timedelta(days=1)
        
        logger.info(f"Completed processing {processed_count} trading days")
        
    except Exception as e:
        logger.error(f"Error in date range processing: {e}")

def show_timepoint_statistics():
    """Show statistics about stored timepoint data"""
    try:
        with engine.connect() as connection:
            stats_sql = """
            SELECT 
                COUNT(*) as total_records,
                MIN(trade_date) as earliest_date,
                MAX(trade_date) as latest_date,
                COUNT(CASE WHEN time_935_price IS NOT NULL THEN 1 END) as records_with_935,
                COUNT(CASE WHEN time_940_price IS NOT NULL THEN 1 END) as records_with_940,
                COUNT(CASE WHEN time_945_price IS NOT NULL THEN 1 END) as records_with_945
            FROM stock_timepoint_data 
            WHERE stock_code = :stock_code
            """
            
            result = connection.execute(text(stats_sql), {'stock_code': TARGET_STOCK}).fetchone()
            
            logger.info(f"\n{'='*50}")
            logger.info(f"TIMEPOINT DATA STATISTICS - {TARGET_STOCK}")
            logger.info(f"{'='*50}")
            logger.info(f"Total Records: {result[0]}")
            logger.info(f"Date Range: {result[1]} to {result[2]}")
            logger.info(f"Records with 9:35 data: {result[3]}")
            logger.info(f"Records with 9:40 data: {result[4]}")
            logger.info(f"Records with 9:45 data: {result[5]}")
            logger.info(f"{'='*50}")
            
    except Exception as e:
        logger.error(f"Error showing statistics: {e}")

if __name__ == "__main__":
    logger.info("Starting QMT timepoint data fetch...")
    
    # Create table
    create_timepoint_table()
    
    # Example usage - fetch data for the last 30 days
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    logger.info(f"Fetching timepoint data for {TARGET_STOCK} from {start_date} to {end_date}")
    
    # Fetch data
    fetch_timepoint_data_for_date_range(start_date, end_date)
    
    # Show statistics
    show_timepoint_statistics()
    
    logger.info("QMT timepoint data fetch completed!")
