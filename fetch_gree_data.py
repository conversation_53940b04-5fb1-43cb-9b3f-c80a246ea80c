import tushare as ts
import pandas as pd
from sqlalchemy import create_engine
import os
import time
from datetime import datetime, timedelta

# Configuration
TUSHARE_TOKEN = "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306/qtdb?charset=utf8mb4"

# Initialize Tushare
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

# Create database engine
engine = create_engine(DATABASE_URL)

def fetch_all_stocks_daily_data(start_date=None, end_date=None, reweight_factor=1.0):
    """
    Fetch daily data for all stocks with daily indicators
    Creates separate table for each stock due to API limit of 10 stocks per request
    Rate limited to 1000 requests per minute
    """
    try:
        # Get all stock codes
        stock_list = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name')
        total_stocks = len(stock_list)
        print(f"Total stocks to process: {total_stocks}")
        
        processed_count = 0
        request_count = 0
        start_time = time.time()
        failed_stocks = []  # Track failed stocks for retry
        
        for _, stock in stock_list.iterrows():
            ts_code = stock['ts_code']
            symbol = stock['symbol']
            name = stock['name']
            
            try:
                # Rate limiting: check if we need to wait
                if request_count >= 900:  # Leave buffer for safety
                    elapsed = time.time() - start_time
                    if elapsed < 60:
                        wait_time = 60 - elapsed
                        print(f"Rate limit reached, waiting {wait_time:.1f} seconds...")
                        time.sleep(wait_time)
                    
                    # Reset counters
                    request_count = 0
                    start_time = time.time()
                
                # Get daily price data
                df_daily = pro.daily(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date
                )
                request_count += 1
                
                if df_daily.empty:
                    print(f"No data for {ts_code} ({name})")
                    continue
                
                # Get daily indicators
                df_basic = pro.daily_basic(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date,
                    fields='ts_code,trade_date,close,turnover_rate,turnover_rate_f,volume_ratio,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,total_share,float_share,free_share,total_mv,circ_mv'
                )
                request_count += 1
                
                # Merge data
                df = pd.merge(df_daily, df_basic, on=['ts_code', 'trade_date'], how='left', suffixes=('', '_basic'))
                
                # Add re-weighting factor
                df['reweight_factor'] = reweight_factor
                df['reweighted_close'] = df['close'] * df['reweight_factor']
                df['reweighted_open'] = df['open'] * df['reweight_factor']
                df['reweighted_high'] = df['high'] * df['reweight_factor']
                df['reweighted_low'] = df['low'] * df['reweight_factor']
                
                # Create table name using stock symbol
                table_name = f'stock_daily_{symbol}'
                
                # Store to database
                df.to_sql(
                    name=table_name,
                    con=engine,
                    if_exists='append',
                    index=False
                )
                
                processed_count += 1
                print(f"Processed {processed_count}/{total_stocks}: {ts_code} ({name}) - {len(df)} records (Requests: {request_count})")
                
            except Exception as stock_error:
                print(f"Error processing {ts_code} ({name}): {stock_error}")
                failed_stocks.append({'ts_code': ts_code, 'symbol': symbol, 'name': name, 'error': str(stock_error)})
                continue
        
        # Retry failed stocks
        if failed_stocks:
            print(f"\nRetrying {len(failed_stocks)} failed stocks...")
            retry_count = 0
            
            for stock_info in failed_stocks:
                ts_code = stock_info['ts_code']
                symbol = stock_info['symbol']
                name = stock_info['name']
                
                try:
                    # Rate limiting for retry
                    if request_count >= 900:
                        elapsed = time.time() - start_time
                        if elapsed < 60:
                            wait_time = 60 - elapsed
                            print(f"Rate limit reached during retry, waiting {wait_time:.1f} seconds...")
                            time.sleep(wait_time)
                        request_count = 0
                        start_time = time.time()
                    
                    # Retry data fetch
                    df_daily = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
                    request_count += 1
                    
                    if df_daily.empty:
                        continue
                    
                    df_basic = pro.daily_basic(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date,
                        fields='ts_code,trade_date,close,turnover_rate,turnover_rate_f,volume_ratio,pe,pe_ttm,pb,ps,ps_ttm,dv_ratio,dv_ttm,total_share,float_share,free_share,total_mv,circ_mv'
                    )
                    request_count += 1
                    
                    df = pd.merge(df_daily, df_basic, on=['ts_code', 'trade_date'], how='left', suffixes=('', '_basic'))
                    
                    # Add re-weighting columns
                    df['reweight_factor'] = reweight_factor
                    df['reweighted_close'] = df['close'] * df['reweight_factor']
                    df['reweighted_open'] = df['open'] * df['reweight_factor']
                    df['reweighted_high'] = df['high'] * df['reweight_factor']
                    df['reweighted_low'] = df['low'] * df['reweight_factor']
                    
                    table_name = f'stock_daily_{symbol}'
                    df.to_sql(name=table_name, con=engine, if_exists='append', index=False)
                    
                    retry_count += 1
                    processed_count += 1
                    print(f"Retry success {retry_count}/{len(failed_stocks)}: {ts_code} ({name})")
                    
                except Exception as retry_error:
                    print(f"Retry failed for {ts_code} ({name}): {retry_error}")
                    continue
        
        print(f"\nFinal results:")
        print(f"Successfully processed: {processed_count} stocks")
        print(f"Failed stocks: {len(failed_stocks) - retry_count}")
        return processed_count
        
    except Exception as e:
        print(f"Error: {e}")
        return None

if __name__ == "__main__":
    # Fetch data for all stocks
    result = fetch_all_stocks_daily_data()




