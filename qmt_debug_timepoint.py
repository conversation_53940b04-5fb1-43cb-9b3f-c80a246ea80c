# coding:gbk
"""
QMT调试版时间点数据获取策略
增加详细的调试信息和错误处理
"""

from datetime import datetime, timedelta
import pandas as pd

# 配置参数
TARGET_STOCK = "000001.SZ"
TARGET_TIMES = ["093500", "094000", "094500"]
TIME_LABELS = ["9:35", "9:40", "9:45"]

def test_basic_api(ContextInfo):
    """测试基本API功能"""
    print("=== 测试基本API功能 ===")
    
    try:
        # 测试获取最新数据
        print("1. 测试获取最新tick数据...")
        tick_data = ContextInfo.get_full_tick([TARGET_STOCK])
        if tick_data and TARGET_STOCK in tick_data:
            print(f"✓ Tick数据获取成功: {tick_data[TARGET_STOCK]['lastPrice']}")
        else:
            print("✗ Tick数据获取失败")
        
        # 测试获取日线数据
        print("2. 测试获取日线数据...")
        daily_data = ContextInfo.get_market_data_ex(
            fields=['close', 'volume'],
            stock_code=[TARGET_STOCK],
            period='1d',
            count=5
        )
        if daily_data and TARGET_STOCK in daily_data and not daily_data[TARGET_STOCK].empty:
            print(f"✓ 日线数据获取成功: {len(daily_data[TARGET_STOCK])} 条记录")
            print(f"  最新收盘价: {daily_data[TARGET_STOCK]['close'].iloc[-1]}")
        else:
            print("✗ 日线数据获取失败")
            
        # 测试获取分钟数据
        print("3. 测试获取分钟数据...")
        minute_data = ContextInfo.get_market_data_ex(
            fields=['close', 'volume'],
            stock_code=[TARGET_STOCK],
            period='1m',
            count=10
        )
        if minute_data and TARGET_STOCK in minute_data and not minute_data[TARGET_STOCK].empty:
            print(f"✓ 分钟数据获取成功: {len(minute_data[TARGET_STOCK])} 条记录")
        else:
            print("✗ 分钟数据获取失败")
            
    except Exception as e:
        print(f"API测试失败: {e}")

def get_timepoint_data_debug(ContextInfo, trade_date):
    """调试版本的数据获取函数"""
    print(f"\n=== 调试获取 {trade_date} 的数据 ===")
    
    try:
        # 方法1: 先尝试获取当天的所有5分钟数据
        print("方法1: 获取当天5分钟数据...")
        start_time = trade_date + "093000"
        end_time = trade_date + "150000"  # 扩大到收盘时间
        
        data1 = ContextInfo.get_market_data_ex(
            fields=['time', 'close', 'volume', 'amount', 'open', 'high', 'low'],
            stock_code=[TARGET_STOCK],
            period='5m',
            start_time=start_time,
            end_time=end_time,
            fill_data=True,
            subscribe=False
        )
        
        if data1 and TARGET_STOCK in data1 and not data1[TARGET_STOCK].empty:
            df1 = data1[TARGET_STOCK]
            print(f"✓ 方法1成功: 获取到 {len(df1)} 条5分钟数据")
            print(f"  时间范围: {df1.index[0]} 到 {df1.index[-1]}")
            return process_5min_data(df1, trade_date)
        else:
            print("✗ 方法1失败")
        
        # 方法2: 尝试获取最近的数据
        print("方法2: 获取最近5分钟数据...")
        data2 = ContextInfo.get_market_data_ex(
            fields=['time', 'close', 'volume', 'amount', 'open', 'high', 'low'],
            stock_code=[TARGET_STOCK],
            period='5m',
            count=50  # 获取最近50条
        )
        
        if data2 and TARGET_STOCK in data2 and not data2[TARGET_STOCK].empty:
            df2 = data2[TARGET_STOCK]
            print(f"✓ 方法2成功: 获取到 {len(df2)} 条5分钟数据")
            print(f"  时间范围: {df2.index[0]} 到 {df2.index[-1]}")
            
            # 筛选指定日期的数据
            df2_filtered = df2[df2.index.astype(str).str.contains(trade_date)]
            if not df2_filtered.empty:
                print(f"  找到 {len(df2_filtered)} 条指定日期的数据")
                return process_5min_data(df2_filtered, trade_date)
            else:
                print(f"  未找到 {trade_date} 的数据")
        else:
            print("✗ 方法2失败")
        
        # 方法3: 尝试1分钟数据
        print("方法3: 尝试1分钟数据...")
        data3 = ContextInfo.get_market_data_ex(
            fields=['time', 'close', 'volume', 'amount'],
            stock_code=[TARGET_STOCK],
            period='1m',
            start_time=start_time,
            end_time=trade_date + "095000",
            fill_data=True,
            subscribe=False
        )
        
        if data3 and TARGET_STOCK in data3 and not data3[TARGET_STOCK].empty:
            df3 = data3[TARGET_STOCK]
            print(f"✓ 方法3成功: 获取到 {len(df3)} 条1分钟数据")
            return process_1min_data(df3, trade_date)
        else:
            print("✗ 方法3失败")
            
        return None
        
    except Exception as e:
        print(f"获取数据异常: {e}")
        return None

def process_5min_data(df, trade_date):
    """处理5分钟数据"""
    print("处理5分钟数据...")
    
    result = {
        'stock_code': TARGET_STOCK,
        'trade_date': trade_date,
        'time_935_price': None, 'time_935_volume': None, 'time_935_amount': None,
        'time_940_price': None, 'time_940_volume': None, 'time_940_amount': None,
        'time_945_price': None, 'time_945_volume': None, 'time_945_amount': None
    }
    
    # 显示所有可用的时间点
    print(f"可用时间点: {list(df.index)}")
    
    df_copy = df.copy()
    df_copy['time_str'] = df_copy.index.astype(str)
    
    for i, target_time in enumerate(TARGET_TIMES):
        matching_rows = df_copy[df_copy['time_str'].str.contains(target_time)]
        
        if not matching_rows.empty:
            row = matching_rows.iloc[0]
            time_key = f"time_{target_time[:3]}{target_time[3:]}"
            
            result[f'{time_key}_price'] = float(row['close']) if pd.notna(row['close']) else None
            result[f'{time_key}_volume'] = int(row['volume']) if pd.notna(row['volume']) else None
            result[f'{time_key}_amount'] = float(row['amount']) if pd.notna(row['amount']) else None
            
            print(f"✓ 找到 {TIME_LABELS[i]}: 价格={row['close']:.3f}")
        else:
            print(f"✗ 未找到 {TIME_LABELS[i]} 数据")
    
    return result

def process_1min_data(df, trade_date):
    """处理1分钟数据"""
    print("处理1分钟数据...")
    
    result = {
        'stock_code': TARGET_STOCK,
        'trade_date': trade_date,
        'time_935_price': None, 'time_935_volume': None, 'time_935_amount': None,
        'time_940_price': None, 'time_940_volume': None, 'time_940_amount': None,
        'time_945_price': None, 'time_945_volume': None, 'time_945_amount': None
    }
    
    df_copy = df.copy()
    df_copy['time_str'] = df_copy.index.astype(str)
    
    for i, target_time in enumerate(TARGET_TIMES):
        matching_rows = df_copy[df_copy['time_str'].str.contains(target_time)]
        
        if not matching_rows.empty:
            row = matching_rows.iloc[0]
            time_key = f"time_{target_time[:3]}{target_time[3:]}"
            
            result[f'{time_key}_price'] = float(row['close']) if pd.notna(row['close']) else None
            result[f'{time_key}_volume'] = int(row['volume']) if pd.notna(row['volume']) else None
            result[f'{time_key}_amount'] = float(row['amount']) if pd.notna(row['amount']) else None
            
            print(f"✓ 找到 {TIME_LABELS[i]}: 价格={row['close']:.3f}")
        else:
            print(f"✗ 未找到 {TIME_LABELS[i]} 数据")
    
    return result

def save_results_to_file(results, filename="debug_timepoint_data.csv"):
    """保存结果到CSV文件"""
    try:
        if not results:
            print("没有数据需要保存")
            return
        
        df = pd.DataFrame(results)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已保存到: {filename}")
        print(f"共保存 {len(df)} 条记录")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

def init(ContextInfo):
    """初始化函数"""
    print("=== QMT调试版时间点数据获取策略 ===")
    print(f"目标股票: {TARGET_STOCK}")
    print(f"目标时间点: {TIME_LABELS}")
    
    # 先测试基本API
    test_basic_api(ContextInfo)
    
    # 设置处理参数
    ContextInfo.results = []
    ContextInfo.current_index = 0
    
    # 只处理最近3个交易日进行调试
    end_date = datetime.now()
    date_list = []
    
    for i in range(10):
        check_date = end_date - timedelta(days=i)
        if check_date.weekday() < 5:
            date_list.append(check_date.strftime('%Y%m%d'))
        if len(date_list) >= 3:  # 只处理3天
            break
    
    ContextInfo.date_list = sorted(date_list)
    print(f"调试模式: 将处理以下日期: {ContextInfo.date_list}")

def handlebar(ContextInfo):
    """主处理函数"""
    try:
        if ContextInfo.current_index < len(ContextInfo.date_list):
            trade_date = ContextInfo.date_list[ContextInfo.current_index]
            
            result = get_timepoint_data_debug(ContextInfo, trade_date)
            
            if result:
                ContextInfo.results.append(result)
                print(f"✓ 完成 {trade_date}")
            else:
                print(f"✗ 失败 {trade_date}")
            
            ContextInfo.current_index += 1
            
        else:
            # 保存结果
            if ContextInfo.results:
                filename = f"debug_timepoint_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                save_results_to_file(ContextInfo.results, filename)
                print(f"\n=== 调试完成 ===")
                print(f"成功处理: {len(ContextInfo.results)} 个交易日")
            else:
                print("调试模式: 没有获取到任何数据")
            
            ContextInfo.results = []
            ContextInfo.current_index = len(ContextInfo.date_list)
            
    except Exception as e:
        print(f"处理过程中出错: {e}")

def after_init(ContextInfo):
    """初始化后函数"""
    print("调试策略初始化完成...")

# 简单测试函数
def simple_test(ContextInfo):
    """简单测试当前数据"""
    print("=== 简单测试 ===")
    today = datetime.now().strftime('%Y%m%d')
    result = get_timepoint_data_debug(ContextInfo, today)
    if result:
        print("测试成功!")
        save_results_to_file([result], f"simple_test_{today}.csv")
    else:
        print("测试失败!")
