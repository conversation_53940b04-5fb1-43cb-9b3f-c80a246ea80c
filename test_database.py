#!/usr/bin/env python3
"""
Test script to verify database creation and connection
"""

import logging
from sqlalchemy import create_engine, text

# Configuration
DATABASE_NAME = "Daily Line"
DATABASE_URL = f"mysql+pymysql://root:12345678@localhost:3306/{DATABASE_NAME}?charset=utf8mb4"
BASE_DATABASE_URL = "mysql+pymysql://root:12345678@localhost:3306?charset=utf8mb4"

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_creation():
    """Test database creation and basic operations"""
    try:
        # Test 1: Create database if not exists
        logger.info("Testing database creation...")
        base_engine = create_engine(BASE_DATABASE_URL)
        
        with base_engine.connect() as connection:
            # Check if database exists
            result = connection.execute(
                text(f"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{DATABASE_NAME}'")
            )
            
            if not result.fetchone():
                # Database doesn't exist, create it
                connection.execute(text(f"CREATE DATABASE `{DATABASE_NAME}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                connection.commit()
                logger.info(f"✓ Created database: {DATABASE_NAME}")
            else:
                logger.info(f"✓ Database '{DATABASE_NAME}' already exists")
        
        base_engine.dispose()
        
        # Test 2: Connect to the specific database
        logger.info("Testing database connection...")
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as connection:
            # Test basic query
            result = connection.execute(text("SELECT DATABASE() as current_db"))
            current_db = result.fetchone()[0]
            logger.info(f"✓ Connected to database: {current_db}")
            
            # Test table creation
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS test_table (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    test_data VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            """))
            connection.commit()
            logger.info("✓ Test table created successfully")
            
            # Test data insertion
            connection.execute(text("""
                INSERT INTO test_table (test_data) VALUES ('Database test successful')
            """))
            connection.commit()
            logger.info("✓ Test data inserted successfully")
            
            # Test data retrieval
            result = connection.execute(text("SELECT * FROM test_table ORDER BY id DESC LIMIT 1"))
            row = result.fetchone()
            logger.info(f"✓ Test data retrieved: {row[1]}")
            
            # Clean up test table
            connection.execute(text("DROP TABLE test_table"))
            connection.commit()
            logger.info("✓ Test table cleaned up")
        
        engine.dispose()
        logger.info(f"\n🎉 All database tests passed! Database '{DATABASE_NAME}' is ready for use.")
        
    except Exception as e:
        logger.error(f"❌ Database test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    logger.info("Starting database connectivity test...")
    success = test_database_creation()
    
    if success:
        logger.info("Database is ready for stock data fetching!")
    else:
        logger.error("Please fix database issues before running the main script.")
